{"createdAt": "2025-06-20T19:58:20.906Z", "updatedAt": "2025-06-20T20:19:01.649Z", "id": "R0xbN7OeiSOW1rY2", "name": "github_workflows_backup", "active": false, "isArchived": false, "nodes": [{"parameters": {"rule": {"interval": [{}]}}, "id": "6e763e09-136b-4cb0-9a57-1d0bdb64ac26", "name": "Schedule Triggerz", "type": "n8n-nodes-base.scheduleTrigger", "position": [-200, 140], "typeVersion": 1.2}, {"parameters": {"filters": {}, "requestOptions": {}}, "id": "6aa78fa9-dce9-46ab-bb0b-a64b2a1ba533", "name": "Retrieve workflows", "type": "n8n-nodes-base.n8n", "position": [340, 140], "typeVersion": 1, "credentials": {"n8nApi": {"id": "KOqGA1i2EbNkTR2k", "name": "n8n account"}}}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "name"}]}, "options": {}}, "id": "f659e0c7-85cf-4870-b199-8e31126e5535", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [180, 140], "typeVersion": 1}, {"parameters": {"resource": "file", "operation": "list", "owner": {"__rl": true, "value": "RustedVikingOG", "mode": "name"}, "repository": {"__rl": true, "value": "n8n_docker_stack", "mode": "list", "cachedResultName": "n8n_docker_stack", "cachedResultUrl": "https://github.com/RustedVikingOG/n8n_docker_stack"}, "filePath": "=/workflows"}, "id": "9495cc65-fade-430c-a325-cb45fc8c3049", "name": "List files from repo", "type": "n8n-nodes-base.github", "position": [20, 140], "webhookId": "78dc5f91-509a-4627-94df-017923d21f8d", "typeVersion": 1, "alwaysOutputData": true, "credentials": {"githubApi": {"id": "uJglpW9ievZkAsDT", "name": "GitHub account"}}}, {"parameters": {"resource": "file", "operation": "edit", "owner": {"__rl": true, "value": "RustedVikingOG", "mode": "name"}, "repository": {"__rl": true, "value": "n8n_docker_stack", "mode": "list", "cachedResultName": "n8n_docker_stack", "cachedResultUrl": "https://github.com/RustedVikingOG/n8n_docker_stack"}, "filePath": "=workflows/{{$('Retrieve workflows').item.json.name\n.trim().replace(/\\s+/g, '-').toLowerCase()\n}}.json", "fileContent": "={{ $('To base64').item.json.data }}", "commitMessage": "=n8n_backup-{{ $('Commit date & file name').item.json.commitDate }}"}, "id": "c3745791-cce5-4283-96a0-eac32aaa5e3b", "name": "Update file", "type": "n8n-nodes-base.github", "position": [1140, 60], "webhookId": "bd99a162-fb2c-41a1-9a79-b3909cfc398c", "typeVersion": 1, "credentials": {"githubApi": {"id": "uJglpW9ievZkAsDT", "name": "GitHub account"}}}, {"parameters": {"resource": "file", "owner": {"__rl": true, "value": "RustedVikingOG", "mode": "name"}, "repository": {"__rl": true, "value": "n8n_docker_stack", "mode": "list", "cachedResultName": "n8n_docker_stack", "cachedResultUrl": "https://github.com/RustedVikingOG/n8n_docker_stack"}, "filePath": "={{$('Retrieve workflows').item.json.name\n.trim().replace(/\\s+/g, '-').toLowerCase()\n}}.json", "fileContent": "={{ $('To base64').item.json.data }}", "commitMessage": "=backup-{{ $node['Commit date & file name'].json.commitDate }}"}, "id": "e5902eb8-efbb-44a9-8286-97afecc1cab2", "name": "Upload file", "type": "n8n-nodes-base.github", "position": [1140, 220], "webhookId": "ed4c0065-237b-4f80-b971-58e5e8d388fa", "typeVersion": 1, "credentials": {"githubApi": {"id": "uJglpW9ievZkAsDT", "name": "GitHub account"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{ $('Aggregate').item.json.name }}", "operation": "contains", "value2": "={{$('Retrieve workflows').item.json.name\n.trim().replace(/\\s+/g, '-').toLowerCase()\n}}.json"}]}}, "id": "c36d97b6-b8bb-47c8-b306-136eb91cc30c", "name": "Check if file exists", "type": "n8n-nodes-base.if", "position": [980, 140], "typeVersion": 1}, {"parameters": {"operation": "to<PERSON><PERSON>", "mode": "each", "options": {"format": true}}, "id": "e2183d38-0277-4327-8afe-3a1d55cdc0aa", "name": "Json file", "type": "n8n-nodes-base.convertToFile", "position": [500, 140], "typeVersion": 1.1}, {"parameters": {"operation": "binaryToPropery", "options": {}}, "id": "cf6d7d10-0d95-4f24-a2c7-cfcdfe0cfb64", "name": "To base64", "type": "n8n-nodes-base.extractFromFile", "position": [660, 140], "typeVersion": 1}, {"parameters": {"content": "### Retrieve previous file names from Github", "height": 380, "width": 300, "color": 5}, "id": "5902da31-f353-407b-a0c2-368520b05a04", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "typeVersion": 1}, {"parameters": {"assignments": {"assignments": [{"id": "fe4a36ef-9f04-40e3-99bd-cc517a49b440", "name": "commitDate", "type": "string", "value": "={{ $now.format('dd-MM-yyyy/H:mm') }}"}, {"id": "b0fe1bcc-e79c-4a6b-b8b4-44222c8bf4e8", "name": "=fileName", "type": "string", "value": "={{$('Retrieve workflows').item.json.name\n.trim().replace(/\\s+/g, '-').toLowerCase()\n}}.json"}]}, "options": {}}, "id": "bf94c705-47f9-4f2a-831e-2bbc9322c621", "name": "Commit date & file name", "type": "n8n-nodes-base.set", "position": [820, 140], "typeVersion": 3.4}, {"parameters": {"content": "### Retrieve and process workflows from n8n", "height": 380, "width": 460, "color": 3}, "id": "087e85e1-9030-49a5-a66a-0091ba72eb31", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [320, 0], "typeVersion": 1}, {"parameters": {"content": "### Commit + edit/create files if needed", "height": 380, "width": 460, "color": 4}, "id": "04b8d4fc-94c6-4c85-a34f-15e1553ded28", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [800, 0], "typeVersion": 1}], "connections": {"Aggregate": {"main": [[{"node": "Retrieve workflows", "type": "main", "index": 0}]]}, "Json file": {"main": [[{"node": "To base64", "type": "main", "index": 0}]]}, "To base64": {"main": [[{"node": "Commit date & file name", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "List files from repo", "type": "main", "index": 0}]]}, "Retrieve workflows": {"main": [[{"node": "Json file", "type": "main", "index": 0}]]}, "Check if file exists": {"main": [[{"node": "Update file", "type": "main", "index": 0}], [{"node": "Upload file", "type": "main", "index": 0}]]}, "List files from repo": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Commit date & file name": {"main": [[{"node": "Check if file exists", "type": "main", "index": 0}]]}, "Schedule Triggerz": {"main": [[{"node": "List files from repo", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": {"node:Schedule Triggerz": {"recurrenceRules": []}}, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "a580d491-524a-4228-adeb-e0c3d713e4b5", "triggerCount": 1, "tags": [{"createdAt": "2025-06-20T15:54:28.386Z", "updatedAt": "2025-06-20T15:54:28.386Z", "id": "oiAlopPbS90SR7YK", "name": "github"}]}