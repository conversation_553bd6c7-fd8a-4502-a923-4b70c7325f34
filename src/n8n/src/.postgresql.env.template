# PostgreSQL Configuration Environment Variables
# Copy this file to .postgresql.env and customize the values for your environment

# PostgreSQL Database Configuration
POSTGRES_DB=n8n
POSTGRES_USER=n8n
POSTGRES_PASSWORD=n8n_password

# PostgreSQL Connection Settings
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# PostgreSQL Performance Settings (optional)
POSTGRES_SHARED_BUFFERS=256MB
POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
POSTGRES_MAINTENANCE_WORK_MEM=64MB
POSTGRES_CHECKPOINT_COMPLETION_TARGET=0.9
POSTGRES_WAL_BUFFERS=16MB
POSTGRES_DEFAULT_STATISTICS_TARGET=100

# PostgreSQL Logging (optional)
POSTGRES_LOG_STATEMENT=none
POSTGRES_LOG_MIN_DURATION_STATEMENT=-1
POSTGRES_LOG_CHECKPOINTS=off
POSTGRES_LOG_CONNECTIONS=off
POSTGRES_LOG_DISCONNECTIONS=off
POSTGRES_LOG_LOCK_WAITS=off

# PostgreSQL Locale Settings
POSTGRES_INITDB_ARGS=--encoding=UTF-8 --locale=C

# PostgreSQL Authentication
POSTGRES_HOST_AUTH_METHOD=md5

# PostgreSQL Data Directory (internal container path)
PGDATA=/var/lib/postgresql/data

# PostgreSQL Backup Settings (optional)
POSTGRES_BACKUP_ENABLED=false
POSTGRES_BACKUP_SCHEDULE=0 2 * * *
POSTGRES_BACKUP_RETENTION_DAYS=7
