services:
  postgres:
    image: postgres:15
    restart: unless-stopped
    env_file:
      - .postgresql.env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -h localhost -U n8n -d n8n']
      interval: 5s
      timeout: 5s
      retries: 10

  workflow-importer:
    build: .
    restart: "no"
    env_file:
      - .n8n.env
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./scripts:/scripts
      - ./workflows:/workflows
    depends_on:
      postgres:
        condition: service_healthy
    entrypoint: ["sh", "-c"]
    command: ["/scripts/import-workflows.sh"]

  n8n:
    build: .
    restart: unless-stopped
    env_file:
      - .n8n.env
    ports:
      - "5678:5678"
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/workflows
      - ./localfiles:/files
    depends_on:
      postgres:
        condition: service_healthy
      workflow-importer:
        condition: service_completed_successfully

  ollama:
    image: ollama/ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama-data:/root/.ollama
    restart: unless-stopped
    entrypoint: ["/bin/sh", "-c", "ollama serve & sleep 3 && ollama run llama3.2:3b && wait"]
    deploy:
      resources:
        limits:
          memory: 4G

volumes:
  postgres_data:
  n8n_data:
  ollama-data: