# COLLABORATION GUIDE: n8n Docker Stack Project

## Overview

A complete Docker Compose setup for running n8n (workflow automation platform) with PostgreSQL database, automatic workflow import, and Ollama AI integration. This project provides a production-ready n8n instance with persistent data storage, pre-configured workflows, and seamless container orchestration.

---

## Project Structure & Responsibilities

Complete Docker-based n8n automation platform with PostgreSQL database and workflow management.

```
/ (root)
├── README.md - Project overview and quick start guide
├── LICENSE - Project license information
├── CHANGELOG.md - Project change history
├── docs/ - Project documentation and design specifications
│   ├── 1.COLLABORATION.md - This collaboration guide
│   └── designs/ - Architecture and use case documentation
│       ├── architecture.md - System architecture with component diagrams
│       ├── use_cases.md - Detailed use cases with state diagrams
│       └── setup_guide.md - Comprehensive setup and configuration guide
└── src/ - Main project source code and configuration
    └── n8n/ - n8n Docker stack implementation
        └── src/ - Core n8n Docker configuration and files
            ├── .n8n.env - n8n service configuration (database, auth, execution settings) [create from .n8n.env.template]
            ├── .n8n.env.template - Template for n8n environment configuration
            ├── .postgresql.env - PostgreSQL database credentials [create from .postgresql.env.template]
            ├── .postgresql.env.template - Template for PostgreSQL environment configuration
            ├── docker-compose.yml - Multi-service orchestration (n8n, PostgreSQL, Ollama, workflow-importer)
            ├── Dockerfile - Custom n8n image with jq and zip utilities
            ├── scripts/
            │   └── import-workflows.sh - Automated workflow import script
            ├── workflows/ - Pre-configured n8n workflow JSON files (auto-imported on startup)
            │   ├── content.md - Documentation for workflow purposes
            │   ├── purpose.md - Workflow collection overview
            │   ├── delete_archived_workflows.json - Cleanup automation
            │   ├── github_repo_workflows_sync.json - GitHub repository synchronization
            │   ├── github_workflows_backup.json - Backup automation for GitHub workflows
            │   ├── gtree_build_context.json - Context building for project trees
            │   ├── gtree_creator.json - Project tree generation workflow
            │   ├── gtree_get.json - Project tree retrieval workflow
            │   ├── test_copy.json - Testing and copying utilities
            │   ├── zip_make.json - Archive creation workflow
            │   └── zip_send.json - Archive distribution workflow
            └── localfiles/ - Local file storage mounted to n8n container
                ├── purpose.md - Documentation for local file usage
                ├── someserver/ - Example server application with FastAPI
                │   ├── Dockerfile - Python FastAPI server container
                │   ├── main.py - FastAPI application entry point
                │   └── requirements.txt - Python dependencies
                ├── someserver.tar.gz - Compressed server archive
                └── someserver.zip - Server archive in ZIP format
```

---

## Key Technologies

- **n8n**: Open-source workflow automation platform for connecting apps and automating tasks
- **Docker & Docker Compose**: Containerization and multi-service orchestration for consistent deployment
- **PostgreSQL**: Relational database for persistent storage of n8n workflows, credentials, and execution data
- **Ollama**: Local AI model serving platform integrated for AI-powered workflow capabilities
- **Alpine Linux**: Lightweight base OS for custom Docker image with jq and zip utilities
- **Bash Scripting**: Automated workflow import and management scripts
- **JSON**: Workflow definition format for n8n automation configurations

---

## Project Details: File & Directory Responsibilities

### Container Services (Docker Compose)

**Service Structure**
- `postgres`: PostgreSQL 15 database service with health checks and persistent storage
- `workflow-importer`: One-time service for importing JSON workflows on startup
- `n8n`: Main n8n automation platform service with web interface and API
- `ollama`: AI model serving platform for LLM integration in workflows

### Configuration Management

**Docker Configuration**
- `src/n8n/src/docker-compose.yml`: Multi-service orchestration with health checks and service dependencies
- `src/n8n/src/Dockerfile`: Custom n8n image with jq and zip utilities for workflow processing

**Environment Files**
- `src/n8n/src/.n8n.env`: n8n service configuration (database connection, authentication, execution settings)
- `src/n8n/src/.postgresql.env`: PostgreSQL database credentials and connection parameters

### Workflow Management

**Workflow Structure**
- `src/n8n/src/workflows/`: JSON workflow definitions automatically imported on container startup
- `src/n8n/src/scripts/import-workflows.sh`: Intelligent workflow import script with duplicate detection
- Automatic import process runs before n8n service starts to ensure workflows are available immediately

### Data & Storage

**Volume Management**
- `src/n8n/src/localfiles/`: Host directory mounted to `/files` in n8n container for file operations
- `n8n_data`: Docker volume for persistent n8n application data
- `postgres_data`: Docker volume for PostgreSQL database storage
- `ollama-data`: Docker volume for Ollama AI model storage

## Tools

A list of tools required for this project to be run and debugged.

### Tool: Docker & Docker Compose

**Requirements**
- Docker Engine 20.10+ installed and running
- Docker Compose V2 (included with Docker Desktop)
- Minimum 4GB RAM available for containers
- Port 5678 available for n8n web interface
- Port 11434 available for Ollama AI service

**Configurations Docker**
- `src/n8n/src/docker-compose.yml`: Multi-service orchestration with health checks and dependencies
- `src/n8n/src/Dockerfile`: Custom n8n image with jq and zip utilities for workflow processing

### Tool: n8n Workflow Automation

**Requirements**
- PostgreSQL database connection for persistent storage
- Basic authentication credentials configured
- Workflow JSON files in proper n8n export format
- Community packages enabled for extended functionality

**Configurations n8n**
- `src/n8n/src/.n8n.env`: Complete n8n service configuration including database, auth, and execution settings
- `src/n8n/src/workflows/*.json`: Pre-configured workflow definitions for automatic import
- `src/n8n/src/scripts/import-workflows.sh`: Automated workflow import with duplicate detection

### Tool: PostgreSQL Database

**Requirements**
- PostgreSQL 15 compatible environment
- Database initialization with n8n schema
- Persistent volume storage for data retention
- Health check configuration for service dependencies

**Configurations PostgreSQL**
- `src/n8n/src/.postgresql.env`: Database credentials and connection parameters
- Health check configuration in `src/n8n/src/docker-compose.yml` for service readiness

### Tool: Ollama AI Platform

**Requirements**
- 4GB memory limit for AI model operations
- Persistent storage for downloaded AI models
- Network connectivity for model downloads
- Compatible with llama3.2:3b model

**Configurations Ollama**
- Service configuration in `src/n8n/src/docker-compose.yml` with memory limits
- Volume mount for persistent model storage
- Automatic model download on container startup

## Setup & Development

### Prerequisites

- Docker Engine 20.10+ and Docker Compose V2
- Git for repository cloning
- 4GB+ available RAM for containers
- Ports 5678 (n8n) and 11434 (Ollama) available
- Basic understanding of n8n workflow automation concepts

### Install Dependencies

```sh
# Clone the repository
git clone <your-repo-url>
cd n8n_docker_stack

# Navigate to n8n source directory
cd src/n8n/src

# Create required environment files from templates
cp .n8n.env.template .n8n.env
cp .postgresql.env.template .postgresql.env

# ⚠️ IMPORTANT: Edit both files to customize credentials and settings
# See docs/designs/setup_guide.md for detailed configuration instructions

# Start the complete stack
docker-compose up -d

# Verify services are running
docker-compose ps

# Check workflow import logs
docker-compose logs workflow-importer

# Access n8n web interface
open http://localhost:5678
```


## Build & Deployment

### Build

Build custom n8n Docker image with additional utilities and deploy the complete stack:

```sh
# Navigate to n8n source directory
cd src/n8n/src

# Build custom n8n image with jq and zip utilities
docker-compose build

# Start all services with automatic workflow import
docker-compose up -d

# Rebuild and restart specific service
docker-compose up -d --build n8n

# Force rebuild workflow importer
docker-compose up workflow-importer --force-recreate
```

### Deploy

Deploy the n8n automation platform for production or development environments:

```sh
# Production deployment
cd src/n8n/src
docker-compose up -d

# Development deployment with debug logging
cd src/n8n/src
docker-compose up

# Scale specific services (if needed)
cd src/n8n/src
docker-compose up -d --scale n8n=1

# Update to latest n8n version
cd src/n8n/src
docker-compose pull
docker-compose up -d
```

## Debugging

Provides a set of strategies for debugging the n8n Docker stack and workflow automation.

**Debugging Strategy: Container Service Issues**

```sh
# Navigate to n8n source directory
cd src/n8n/src

# Check service status and health
docker-compose ps
docker-compose logs -f n8n
docker-compose logs -f postgres
docker-compose logs workflow-importer

# Restart specific services
docker-compose restart n8n
docker-compose restart postgres

# Rebuild and restart with fresh containers
docker-compose down
docker-compose up -d --build

# Check database connectivity
docker-compose exec postgres psql -U n8n -d n8n -c "\dt"
```

**Debugging Strategy: Workflow Import Issues**

```sh
# Navigate to n8n source directory
cd src/n8n/src

# Check workflow import logs
docker-compose logs workflow-importer

# Manually trigger workflow import
docker-compose up workflow-importer --force-recreate

# Validate workflow JSON files
for file in workflows/*.json; do echo "Checking $file:"; jq empty "$file" && echo "✅ Valid" || echo "❌ Invalid"; done

# Test n8n CLI commands inside container
docker-compose exec n8n n8n list:workflow
docker-compose exec n8n n8n import:workflow --input=/workflows/your-workflow.json
```

**Debugging Strategy: Network and Port Issues**

```sh
# Check port availability
netstat -an | grep 5678
netstat -an | grep 11434

# Test n8n web interface connectivity
curl -I http://localhost:5678

# Check container network connectivity
docker-compose exec n8n ping postgres
docker-compose exec n8n ping ollama

# Verify environment variables
docker-compose exec n8n env | grep N8N
docker-compose exec postgres env | grep POSTGRES
```

**DEBUGGING: GOTCHAs AND RECOVERY**

A list of tips and tricks for detailing with debugging gotchas.
NOTE: !! do not change this.

- The ERROR: "Unexpected eof" (end of file) | Can typically mean there is a missing or extra bracket/parenthesis. However, can be some times caused by mis-aligned quote types (i.e. opening quote: \' while closing quote '") or additionally by missing tags. Please rewrite the given file to fix the error even if it looks correct. Mistakes happen.
- ERROR: "command n8n not found" | Fixed by using official Docker registry image `docker.n8n.io/n8nio/n8n` instead of custom commands
- ERROR: Database connection refused | Ensure PostgreSQL health check passes before n8n starts, check `.postgresql.env` credentials
- ERROR: Port already in use | Change port mapping in docker-compose.yml or stop conflicting services
- ERROR: Workflow import failed | Validate JSON syntax with `jq empty workflow.json`, check workflow format compatibility
- ERROR: Permission denied on volumes | Ensure Docker has proper permissions for volume mount directories
- ERROR: Ollama model download timeout | Increase memory limits or download models manually with `docker-compose exec ollama ollama pull llama3.2:3b`

## Git Commit Best 

The require git commit policies to follow.
NOTE: !! do not change this.

**Git Commands**

- Use the `git status` command to get a clear view of what you are updating.
- Add and commit your changes with a helpful message using `git add -A && git commit -m '[HELPFUL COMMIT MESSAGE HERE]'`

**Basic Rules**
- Git commits should be a wrapper for related changes. For example, fixing two different bugs should produce two separate commits. 
- Commit Often to keep your commits small to enable better reporting on changes and git history management.
- Don't Commit Half-Done Work, only commit code when a logical component is completed. Split a feature's implementation into logical chunks that can be completed quickly so that you can commit often.
- Test Your Code Before You Commit. Follow the Debugging Strategies.
Resist the temptation to commit something that you «think» is completed. Test it thoroughly by making sure the code builds.
- Write clear and Good Commit Messages and keep [.docs/CHANGELOG.md] is up to date. Begin your message with a short summary of your changes (up to 50 characters as a guideline). Separate it from the following body by including a blank line. The body of your message should provide detailed answers to the following questions: – What was the motivation for the change? – How does it differ from the previous implementation? Use the imperative, present tense («change», not «changed» or «changes») to be consistent with generated messages from commands like git merge.


## Collaboration Tips

A summarization of the Collaboration tips and tricks.
NOTE: !! do not change this.

- Keep container services and configurations separated for clarity.
- Use clear commit messages and PR descriptions.
- Document new workflows, configurations, and scripts in the `docs/` folder.
- Update this guide as the project evolves.
- Test workflow imports locally before committing new JSON files.
- Use environment files (.n8n.env, .postgresql.env) for sensitive configuration instead of hardcoding values.
- Validate JSON workflow files with `jq` before adding to the `src/n8n/src/workflows/` directory.
- Document workflow purposes in the `src/n8n/src/workflows/` directory.
- Always run Docker commands from the `src/n8n/src/` directory where `docker-compose.yml` is located.

## 🔬📚 Research

Find all the research docs related to this project in the directory [./docs/research/] (if it exists).

NOTE: !! do not change this.

**‼️ Rules**

- ✅ Always provide link or path reference to resources used from this Research. Use Oxford Academic citing style, both inline and as a footnote.
- ✅ Always prefer research notes and documentation provided here over your own knowledge.

**📝 Notes**

A set of notes, as markdown files of research relating to topics relevant to this project can be found in [./docs/research/note-*.md] (if research directory exists). Reference these to inform implementations related to this project.

**🌐 Confluence Page documents**

Contains a list of relevant [./docs/research/confluence_pages.md] researched in this project following the template [./docs/ai/templates/__confluence_pages.md] (if research directory exists).

**🌐 Web link documents**

Contains a list of relevant [./docs/research/web_links.md] researched in this project following the template [./docs/ai/templates/__web_links.md] (if research directory exists).
